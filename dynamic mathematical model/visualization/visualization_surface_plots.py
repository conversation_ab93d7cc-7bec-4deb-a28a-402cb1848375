import numpy as np
import matplotlib.pyplot as plt
import matplotlib.tri as mtri

def plot_sensor_data_2d(visualizer, node_coords, sensor_data, elements, save_path=None):
    """
    Plot 2D surface plots of sensor data
    
    Args:
        visualizer: ModelVisualizer instance
        node_coords (np.array): Node coordinates (N, 3)
        sensor_data (dict): Sensor data
        elements (np.array): Triangle elements (M, 3)
        save_path (str): Save path
    """
    # 创建三角化对象（仅使用 X, Y 坐标）
    triang = mtri.Triangulation(node_coords[:, 0], node_coords[:, 1], elements[:, :3] - 1)
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    parameters = ['temperature', 'ph', 'conductivity', 'chlorophyll']
    titles = ['Temperature Distribution (°C)', 'pH Distribution', 
             'Conductivity Distribution (μS/cm)', 'Chlorophyll Distribution (μg/L)']
    
    for i, (param, title) in enumerate(zip(parameters, titles)):
        ax = axes[i//2, i%2]
        
        # 绘制面图，使用插值着色
        tcf = ax.tripcolor(triang, sensor_data[param], cmap=visualizer.color_maps[param], shading='gouraud')
        
        ax.set_xlabel('X Coordinate (m)')
        ax.set_ylabel('Y Coordinate (m)')
        ax.set_title(title)
        ax.set_aspect('equal')
        
        # 添加颜色条
        cbar = plt.colorbar(tcf, ax=ax)
        cbar.set_label(param.capitalize())
        
        # 添加统计信息
        data = sensor_data[param]
        stats_text = f'Mean: {np.mean(data):.2f}\nStandard Deviation: {np.std(data):.2f}'
        ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, 
               verticalalignment='top', bbox=dict(boxstyle='round', 
               facecolor='white', alpha=0.8))
    
    plt.tight_layout()
    
    if save_path:
        visualizer.create_directory(save_path)
        plt.savefig(f'{save_path}/sensor_data_2d.png', 
                   dpi=visualizer.dpi, bbox_inches='tight')
    plt.show()

def plot_health_index_2d(visualizer, node_coords, health_index, elements, save_path=None):
    """
    Plot 2D surface plot of health index
    
    Args:
        visualizer: ModelVisualizer instance
        node_coords (np.array): Node coordinates (N, 3)
        health_index (np.array): Health index
        elements (np.array): Triangle elements (M, 3)
        save_path (str): Save path
    """
    # 创建三角化对象
    triang = mtri.Triangulation(node_coords[:, 0], node_coords[:, 1], elements[:, :3] - 1)
    
    fig, ax = plt.subplots(figsize=visualizer.figsize)
    
    # 绘制面图，使用插值着色
    tcf = ax.tripcolor(triang, health_index, cmap=visualizer.color_maps['health'], 
                      shading='gouraud', vmin=0, vmax=1)
    
    ax.set_xlabel('X Coordinate (m)')
    ax.set_ylabel('Y Coordinate (m)')
    ax.set_title('Pearl Mussel Health Index Distribution')
    ax.set_aspect('equal')
    
    # 添加颜色条
    cbar = plt.colorbar(tcf, ax=ax)
    cbar.set_label('Health Index')
    
    # 添加统计信息
    stats_text = f'Mean: {np.mean(health_index):.3f}\n' \
                f'Standard Deviation: {np.std(health_index):.3f}\n' \
                f'Minimum: {np.min(health_index):.3f}\n' \
                f'Maximum: {np.max(health_index):.3f}'
    ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, 
           verticalalignment='top', bbox=dict(boxstyle='round', 
           facecolor='white', alpha=0.9))
    
    plt.tight_layout()
    
    if save_path:
        visualizer.create_directory(save_path)
        plt.savefig(f'{save_path}/health_index_2d.png', 
                   dpi=visualizer.dpi, bbox_inches='tight')
    plt.show()

def plot_3d_visualization(visualizer, node_coords, data, title, colormap, save_path=None):
    """
    Create 3D scatter plot visualization
    
    Args:
        visualizer: ModelVisualizer instance
        node_coords (np.array): Node coordinates
        data (np.array): Data to visualize
        title (str): Figure title
        colormap (str): Colormap
        save_path (str): Save path
    """
    fig = plt.figure(figsize=(12, 9))
    ax = fig.add_subplot(111, projection='3d')
    
    scatter = ax.scatter(node_coords[:, 0], node_coords[:, 1], node_coords[:, 2],
                       c=data, cmap=colormap, s=30, alpha=0.6)
    
    ax.set_xlabel('X Coordinate (m)')
    ax.set_ylabel('Y Coordinate (m)')
    ax.set_zlabel('Z Coordinate (m)')
    ax.set_title('Pearl Mussel Health Index 3D Distribution')
    
    # 添加颜色条
    cbar = plt.colorbar(scatter, ax=ax, shrink=0.5, aspect=20)
    cbar.set_label('Health Index')
    
    plt.tight_layout()
    
    if save_path:
        visualizer.create_directory(save_path)
        filename = title.replace(' ', '_').replace('(', '').replace(')', '').lower()
        plt.savefig(f'{save_path}/{filename}_3d.png', 
                   dpi=visualizer.dpi, bbox_inches='tight')
    plt.show()


# import numpy as np
# import matplotlib.pyplot as plt
# import matplotlib.tri as mtri

# def plot_sensor_data_2d(visualizer, node_coords, sensor_data, elements, save_path=None):
#     """
#     Plot 2D surface plots of sensor data
    
#     Args:
#         visualizer: ModelVisualizer instance
#         node_coords (np.array): Node coordinates (N, 3)
#         sensor_data (dict): Sensor data
#         elements (np.array): Triangle elements (M, 3)
#         save_path (str): Save path
#     """
#     # 创建三角化对象（仅使用 X, Y 坐标）
#     triang = mtri.Triangulation(node_coords[:, 0], node_coords[:, 1], elements[:, :3] - 1)
    
#     fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
#     parameters = ['temperature', 'ph', 'conductivity', 'chlorophyll']
#     titles = ['Temperature Distribution (°C)', 'pH Distribution', 
#              'Conductivity Distribution (μS/cm)', 'Chlorophyll Distribution (μg/L)']
    
#     for i, (param, title) in enumerate(zip(parameters, titles)):
#         ax = axes[i//2, i%2]
        
#         # 绘制面图
#         tcf = ax.tripcolor(triang, sensor_data[param], cmap=visualizer.color_maps[param], shading='flat')
        
#         ax.set_xlabel('X Coordinate (m)')
#         ax.set_ylabel('Y Coordinate (m)')
#         ax.set_title(title)
#         ax.set_aspect('equal')
        
#         # 添加颜色条
#         cbar = plt.colorbar(tcf, ax=ax)
#         cbar.set_label(param.capitalize())
        
#         # 添加统计信息
#         data = sensor_data[param]
#         stats_text = f'Mean: {np.mean(data):.2f}\nStandard Deviation: {np.std(data):.2f}'
#         ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, 
#                verticalalignment='top', bbox=dict(boxstyle='round', 
#                facecolor='white', alpha=0.8))
    
#     plt.tight_layout()
    
#     if save_path:
#         visualizer.create_directory(save_path)
#         plt.savefig(f'{save_path}/sensor_data_2d.png', 
#                    dpi=visualizer.dpi, bbox_inches='tight')
#     plt.show()

# def plot_health_index_2d(visualizer, node_coords, health_index, elements, save_path=None):
#     """
#     Plot 2D surface plot of health index
    
#     Args:
#         visualizer: ModelVisualizer instance
#         node_coords (np.array): Node coordinates (N, 3)
#         health_index (np.array): Health index
#         elements (np.array): Triangle elements (M, 3)
#         save_path (str): Save path
#     """
#     # 创建三角化对象
#     triang = mtri.Triangulation(node_coords[:, 0], node_coords[:, 1], elements[:, :3] - 1)
    
#     fig, ax = plt.subplots(figsize=visualizer.figsize)
    
#     # 绘制面图
#     tcf = ax.tripcolor(triang, health_index, cmap=visualizer.color_maps['health'], 
#                       shading='flat', vmin=0, vmax=1)
    
#     ax.set_xlabel('X Coordinate (m)')
#     ax.set_ylabel('Y Coordinate (m)')
#     ax.set_title('Pearl Mussel Health Index Distribution')
#     ax.set_aspect('equal')
    
#     # 添加颜色条
#     cbar = plt.colorbar AXE = ax)
#     cbar.set_label('Health Index')
    
#     # 添加统计信息
#     stats_text = f'Mean: {np.mean(health_index):.3f}\n' \
#                 f'Standard Deviation: {np.std(health_index):.3f}\n' \
#                 f'Minimum: {np.min(health_index):.3f}\n' \
#                 f'Maximum: {np.max(health_index):.3f}'
#     ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, 
#            verticalalignment='top', bbox=dict(boxstyle='round', 
#            facecolor='white', alpha=0.9))
    
#     plt.tight_layout()
    
#     if save_path:
#         visualizer.create_directory(save_path)
#         plt.savefig(f'{save_path}/health_index_2d.png', 
#                    dpi=visualizer.dpi, bbox_inches='tight')
#     plt.show()

# def plot_3d_visualization(visualizer, node_coords, data, title, colormap, save_path=None):
#     """
#     Create 3D scatter plot visualization
    
#     Args:
#         visualizer: ModelVisualizer instance
#         node_coords (np.array): Node coordinates
#         data (np.array): Data to visualize
#         title (str): Figure title
#         colormap (str): Colormap
#         save_path (str): Save path
#     """
#     fig = plt.figure(figsize=(12, 9))
#     ax = fig.add_subplot(111, projection='3d')
    
#     scatter = ax.scatter(node_coords[:, 0], node_coords[:, 1], node_coords[:, 2],
#                        c=data, cmap=colormap, s=30, alpha=0.6)
    
#     ax.set_xlabel('X Coordinate (m)')
#     ax.set_ylabel('Y Coordinate (m)')
#     ax.set_zlabel('Z Coordinate (m)')
#     ax.set_title('Pearl Mussel Health Index 3D Distribution')
    
#     # 添加颜色条
#     cbar = plt.colorbar(scatter, ax=ax, shrink=0.5, aspect=20)
#     cbar.set_label('Health Index')
    
#     plt.tight_layout()
    
#     if save_path:
#         visualizer.create_directory(save_path)
#         filename = title.replace(' ', '_').replace('(', '').replace(')', '').lower()
#         plt.savefig(f'{save_path}/{filename}_3d.png', 
#                    dpi=visualizer.dpi, bbox_inches='tight')
#     plt.show()