import matplotlib.pyplot as plt

def plot_time_series(visualizer, time_data, health_data, title="Health Index Time Series", save_path=None):
    """
    Plot time series
    
    Args:
        visualizer: ModelVisualizer instance
        time_data (np.array): Time data
        health_data (np.array): Health data
        title (str): Figure title
        save_path (str): Save path
    """
    fig, ax = plt.subplots(figsize=visualizer.figsize)
    
    ax.plot(time_data, health_data, linewidth=2, color='blue')
    ax.set_xlabel('Time (days)')
    ax.set_ylabel('Health Index')
    ax.set_title(title)
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    if save_path:
        visualizer.create_directory(save_path)
        plt.savefig(f'{save_path}/time_series.png', 
                   dpi=visualizer.dpi, bbox_inches='tight')
    plt.show()