import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd

def plot_correlation_matrix(visualizer, sensor_data, health_index, save_path=None):
    """
    Plot correlation matrix of parameters
    
    Args:
        visualizer: ModelVisualizer instance
        sensor_data (dict): Sensor data
        health_index (np.array): Health index
        save_path (str): Save path
    """
    # 准备数据
    data_dict = sensor_data.copy()
    data_dict['health_index'] = health_index
    
    # 创建DataFrame用于相关性分析
    df = pd.DataFrame({
        'Temperature': data_dict['temperature'],
        'pH': data_dict['ph'],
        'Conductivity': data_dict['conductivity'],
        'Chlorophyll': data_dict['chlorophyll'],
        'Health Index': data_dict['health_index']
    })
    
    # 计算相关性矩阵
    correlation_matrix = df.corr()
    
    # 绘制热力图
    fig, ax = plt.subplots(figsize=(10, 8))
    sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0,
               square=True, ax=ax, cbar_kws={'label': 'Correlation Coefficient'})
    ax.set_title('Correlation Analysis of Environmental Parameters and Health Index')
    
    plt.tight_layout()
    
    if save_path:
        visualizer.create_directory(save_path)
        plt.savefig(f'{save_path}/correlation_matrix.png', 
                   dpi=visualizer.dpi, bbox_inches='tight')
    plt.show()