import numpy as np
import matplotlib.pyplot as plt

def plot_parameter_distributions(visualizer, sensor_data, health_index, save_path=None):
    """
    Plot histogram of parameter distributions
    
    Args:
        visualizer: ModelVisualizer instance
        sensor_data (dict): Sensor data
        health_index (np.array): Health index
        save_path (str): Save path
    """
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    parameters = ['temperature', 'ph', 'conductivity', 'chlorophyll']
    titles = ['Temperature (°C)', 'pH', 'Conductivity (μS/cm)', 'Chlorophyll (μg/L)']
    
    # 绘制传感器数据分布
    for i, (param, title) in enumerate(zip(parameters, titles)):
        ax = axes[i//2, i%2]
        data = sensor_data[param]
        
        ax.hist(data, bins=30, alpha=0.7, edgecolor='black', 
               color=plt.cm.Set3(i))
        ax.set_title(f'{title} Distribution')
        ax.set_xlabel(title)
        ax.set_ylabel('Frequency')
        ax.grid(True, alpha=0.3)
        
        # 添加统计线
        mean_val = np.mean(data)
        ax.axvline(mean_val, color='red', linestyle='--', 
                  label=f'Mean: {mean_val:.2f}')
        ax.legend()
    
    # 绘制健康度指数分布
    ax = axes[1, 2]
    ax.hist(health_index, bins=30, alpha=0.7, edgecolor='black', 
           color='lightgreen')
    ax.set_title('Health Index Distribution')
    ax.set_xlabel('Health Index')
    ax.set_ylabel('Frequency')
    ax.grid(True, alpha=0.3)
    
    mean_health = np.mean(health_index)
    ax.axvline(mean_health, color='red', linestyle='--', 
              label=f'Mean: {mean_health:.3f}')
    ax.legend()
    
    plt.tight_layout()
    
    if save_path:
        visualizer.create_directory(save_path)
        plt.savefig(f'{save_path}/parameter_distributions.png', 
                   dpi=visualizer.dpi, bbox_inches='tight')
    plt.show()