import numpy as np
import matplotlib.pyplot as plt
import os

# 设置字体和样式
plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class ModelVisualizer:
    """模型可视化类"""
    
    def __init__(self, figsize=(12, 8), dpi=300):
        """
        Initialize the visualizer
        
        Args:
            figsize (tuple): Default figure size
            dpi (int): Figure resolution
        """
        self.figsize = figsize
        self.dpi = dpi
        self.color_maps = {
            'temperature': 'coolwarm',
            'ph': 'RdYlBu_r',
            'conductivity': 'viridis',
            'chlorophyll': 'Greens',
            'health': 'RdYlGn'
        }
    
    def create_directory(self, path):
        """Create directory if it doesn't exist"""
        if not os.path.exists(path):
            os.makedirs(path)
    
    def create_comprehensive_visualization(self, node_coords, sensor_data, health_index, elements, save_path=None):
        """
        Create comprehensive visualization report
        
        Args:
            node_coords (np.array): Node coordinates
            sensor_data (dict): Sensor data
            health_index (np.array): Health index
            elements (np.array): Triangle elements
            save_path (str): Save path
        """
        from .surface_plots import plot_sensor_data_2d, plot_health_index_2d, plot_3d_visualization
        from .distribution_plots import plot_parameter_distributions
        from .correlation_plots import plot_correlation_matrix
        
        print("Generating 2D sensor data surface plots...")
        plot_sensor_data_2d(self, node_coords, sensor_data, elements, save_path)
        
        print("Generating 2D health index surface plot...")
        plot_health_index_2d(self, node_coords, health_index, elements, save_path)
        
        print("Generating parameter distribution histograms...")
        plot_parameter_distributions(self, sensor_data, health_index, save_path)
        
        print("Generating correlation analysis plot...")
        plot_correlation_matrix(self, sensor_data, health_index, save_path)
        
        # 如果有3D坐标，生成3D可视化
        if node_coords.shape[1] > 2:
            print("Generating 3D scatter visualization...")
            plot_3d_visualization(self, node_coords, health_index, 
                                 'Pearl Mussel Health Index 3D Distribution', 
                                 self.color_maps['health'], save_path)
        
        print("✅ All visualization plots generated successfully!")